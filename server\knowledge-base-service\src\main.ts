import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import * as compression from 'compression';
import * as helmet from 'helmet';
import { AppModule } from './app.module';
import { HttpExceptionFilter, AllExceptionsFilter } from './common/filters/http-exception.filter';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 配置微服务
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: configService.get<string>('KNOWLEDGE_BASE_SERVICE_HOST', 'localhost'),
      port: configService.get<number>('KNOWLEDGE_BASE_SERVICE_PORT', 3008),
    },
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 全局异常过滤器
  app.useGlobalFilters(new AllExceptionsFilter(), new HttpExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(new LoggingInterceptor(), new TransformInterceptor());

  // 启用CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet());

  // API前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('DL引擎知识库服务API')
    .setDescription('提供知识库管理、文档处理、向量存储和语义检索功能')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('knowledge-base', '知识库管理')
    .addTag('documents', '文档管理')
    .addTag('search', '语义检索')
    .addTag('embeddings', '向量嵌入')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 健康检查端点
  app.getHttpAdapter().get('/health', (_req: any, res: any) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'knowledge-base-service',
      version: '1.0.0',
    });
  });

  // 启动微服务
  await app.startAllMicroservices();

  // 启动HTTP服务
  const httpPort = configService.get<number>('KNOWLEDGE_BASE_HTTP_PORT', 4008);
  await app.listen(httpPort);

  console.log(`知识库服务已启动，微服务端口: ${configService.get<number>('KNOWLEDGE_BASE_SERVICE_PORT', 3008)}, HTTP端口: ${httpPort}`);
}

bootstrap();
