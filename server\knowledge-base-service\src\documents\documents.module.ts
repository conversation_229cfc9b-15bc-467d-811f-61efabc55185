import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { DocumentsService } from './documents.service';
import { Document } from './entities/document.entity';
import { DocumentProcessorService } from './processors/document-processor.service';
import { DocumentQueueProcessor } from './processors/document-queue.processor';
import { VectorStoreModule } from '../vector-store/vector-store.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Document]),
    BullModule.registerQueue({
      name: 'document-processing',
    }),
    VectorStoreModule,
  ],
  providers: [
    DocumentsService,
    DocumentProcessorService,
    DocumentQueueProcessor,
  ],
  exports: [DocumentsService],
})
export class DocumentsModule {}
